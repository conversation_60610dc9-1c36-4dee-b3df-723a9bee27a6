<?php

namespace app\api\controller;


use app\admin\model\Channel;
use app\admin\model\Merchants;
use app\api\validata\OrderCreate;
use app\common\controller\Api;
use Rongpay\Allinpaysc;
use think\Db;
use think\Log;
use think\Validate;
use WxNotify;
use think\Exception;
use fast\Http;
use GuzzleHttp\Client;

class Order extends Api
{
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];

    public function newcreate()
    {
        $data = $this->request->param();
        // 实例化验证器
        $validator = new OrderCreate();

        if (!$validator->batch()->check($data)) {
            $this->error($validator->getError());
        }

        $merchants = Merchants::where('code', $data['merchants_code'])->find();
        Db::startTrans();
        try {
            $count = \app\admin\model\Order::where('out_trade_no', $data['out_trade_no'])->lock(true)->count();
            if ($count>0) {
                throw new Exception("订单号已存在");
            } 
            // 加载通道下浮金额配置
            $channel = Channel::where('code', $data['channel_code'])->find();
            // 判断通道是否开启下浮金额
            if ($channel->preferential_status == 1) {
                $preferential = rand($channel->preferential_min, $channel->preferential_max)/100;
            } else {
                $preferential = 0;
            }

            // 计算下浮金额后的订单金额
            $data['amount'] = $data['amount'] - $preferential;

            // 生成订单号
            $local = \app\common\library\Order::createUniqueNo();

            // 创建订单
            $order = new \app\admin\model\Order();
            $order->out_trade_no = $data['out_trade_no'];
            $order->local_trade_no = $local;
            $order->amount = $data['amount'];
            $order->merchants_id = $merchants->id;
            $order->channel_code = $data['channel_code'];
            $order->callback_url = $data['notify_url'];
            $order->preferential = $preferential;
            
            if (isset ($data['account_id']) &&$data['account_id'] != 0) {
                $account = db('account')->where('id',$data['account_id'])->find();
            } else {
                $accountList = $this->getAccountCanUseList2($data['amount'],$data['channel_code']);
                if (!isset($accountList)) {
                    throw new Exception("暂无可用账号1");
                }
                if (empty($accountList)) {
                    throw new Exception("暂无可用账号2");
                }
                if (!isset($accountList[0])) {
                    throw new Exception("暂无可用账号3");
                }
                $account = $accountList[0];
            }
            // $accountList = $this->getAccountCanUseList2($data['amount'],$admin['id'],$data['channel_code']);
            // if (!isset($accountList)) {
            //     throw new Exception("暂无可用账号1");
            // }
            // if (empty($accountList)) {
            //     throw new Exception("暂无可用账号2");
            // }
            // if (!isset($accountList[0])) {
            //     throw new Exception("暂无可用账号3");
            // }
            // $account = $accountList[0];
            // 更新账号拉单时间
            db('account')->where('id',$account['id'])->update(['pulltime'=>date("Y-m-d H:i:s", time())]);
            
            switch($data['channel_code']){
                // 支付小程序预支付
                case '9002':
                    $x_app_id = '****************';
                    $returnData = "alipays://platformapi/startapp?appId=".$x_app_id.'&page='.urlencode('/pages/cashier/cashier?query='.$data['out_trade_no']);
                    // $returnData = $this->request->domain().'/cashier?order='.$local.'&amount='.$data['amount'].'&channel='.$data['channel_code'];
                    // $returnData = $this->request->domain().'/cashier/index/preauth_page.html?trade_no='.$local;
                    break;
                // 支付宝手机网站
                case '9003':
                    $returnData = 'http://pay.sdokjw.com/cashier?order='.$local.'&amount='.$data['amount'].'&channel='.$data['channel_code'];
                    break;
                // 手机网站跳转短剧
                case '9004':
                    $returnData = 'http://pay.sdokjw.com/cashier?order='.$local.'&amount='.$data['amount'].'&channel='.$data['channel_code'];
                    break;
                case '9007':
                    $account_config = json_decode($account['config'],true);
                    $returnData = "alipayqr://platformapi/startapp?appId=********&url=".urlencode("alipays://platformapi/startapp?appId=********&actionType=scan&biz_data=".urlencode("{\"s\":\"money\",\"u\":\"{$account_config['user_id']}\",\"a\":\"{$data['amount']}\",\"m\":\"{$data['out_trade_no']}\"}"));
                case '9008':
                    // 查找对应金额的商品
                    $product = db('account_product')
                        ->where('account_id', $account['id'])
                        ->where('price', $data['amount'])
                        ->find();
                    
                    if (!$product) {
                        throw new Exception("该账号没有对应金额的商品");
                    }
                    
                    $order->notes = $account['name'] . ' - ' .$account['notes'];
                    $returnData = ['sharl_code'=>$product['sharl_code']];
                    break;
                case '9011':
                    // 查找对应金额的商品
                    $product = db('account_product')
                        ->where('account_id', $account['id'])
                        ->where('price', $data['amount'])
                        ->find();
                    
                    if (!$product) {
                        throw new Exception("该账号没有对应金额的商品");
                    }
                    
                    $order->pay_url = $product['product_url'];
                    $order->notes = $account['name'] . ' - ' .$account['notes'];
                    $returnData = ['product_url'=>$product['product_url']];
                    break;
                case '9012':
                    // 查找对应金额的商品
                    $product = db('account_product')
                        ->where('account_id', $account['id'])
                        ->where('price', $data['amount'])
                        ->find();
                    
                    if (!$product) {
                        throw new Exception("该账号没有对应金额的商品");
                    }
                    
                    $order->notes = $account['name'] . ' - ' .$account['notes'];
                    $returnData = ['qrcode'=>$this->convertImageToBase64($product['qrcode'])];
                    break;
                case '9009':
                    $account_config = json_decode($account['config'],true);
                    // 从图片地址读取并转换为base64
                    $returnData = $this->convertImageToBase64($account_config['game_url']);
                    if (empty($returnData)) {
                        throw new Exception("二维码图片读取失败，请检查图片地址是否有效");
                    }
                    break;
                case '9010':
                    $account_config = json_decode($account['config'],true);
                    $base64code = $this->convertImageToBase64($account_config['game_url']);
                    // $res = Http::get('https://api.no0a.cn/api/qrdecode/query?imgurl=http://20.255.43.134:8866'.$account_config['game_url']);
                    // $res = json_decode($res,true);
                    // if (empty($res)) {
                    //     throw new Exception("解析二维码失败");
                    // }
                    // if ($res['status']!=1) {
                    //     throw new Exception("解析二维码失败");
                    // }
                    // 从图片地址读取并转换为base64
                    // $returnData = ['name'=>$account['name'], 'qrcode'=>$base64code, 'qrurl'=>$res['qrurl']];
                    $order->notes = $account['name'] . ' - ' .$account['notes'];
                    $returnData = ['name'=>$account['name'], 'qrcode'=>$base64code];
                    if (empty($returnData)) {
                        throw new Exception("二维码图片读取失败，请检查图片地址是否有效");
                    }
                    break;
                default:
                    $returnData = '';
                    break;
            }
            // 获取账号配置
            // $account_config = json_decode($account['config'],true);

            $order->admin_id = $account['admin_id'];
            $order->account_identity = $account['account_identity'];
            
            $order->save();
            Db::commit();

        } catch (\Exception $e) {
            Db::rollback();
            trace('订单创建失败:'.$e,'error');
            db('admin')->where('id',$admin['id'])->update(['pulltime'=>date("Y-m-d H:i:s", time())]);
            $this->error($e->getMessage());
        }

        $this->success('创建成功',$returnData);
    }

    
    protected function encodeUrlParams($url, $params) {
        // 解析 URL
        $urlParts = parse_url($url);
        // 如果没有查询字符串，则直接返回原 URL
        if (!isset($urlParts['query'])) {
            return $url;
        }
        
        // 解析查询字符串为关联数组
        parse_str($urlParts['query'], $queryParams);
        
        // 对指定的参数进行 urlencode 编码
        foreach ($params as $param) {
            if (isset($queryParams[$param])) {
                $queryParams[$param] = urlencode($queryParams[$param]);
            }
        }
        
        // 重新构建查询字符串
        $newQuery = http_build_query($queryParams);
        
        // 重建 URL
        $newUrl = 'https://' . $urlParts['host'];
        
        if (isset($urlParts['port'])) {
            $newUrl .= ':' . $urlParts['port'];
        }
        
        $newUrl .= $urlParts['path'];
        
        if ($newQuery) {
            $newUrl .= '?' . $newQuery;
        }
        
        if (isset($urlParts['fragment'])) {
            $newUrl .= '#' . $urlParts['fragment'];
        }
        
        return $newUrl;
    }
    
    public function notify()
    {
        $params = $this->request->param();
        trace("短剧回调通知：".json_encode($params),'error');
        // Log::write(json_encode($params),'notice');
        // Log::write($params['out_trade_no'],'notice');
        $out_trade_no = $params['out_trade_no'];
        $trade_status = $params['trade_status'];
        $order = \app\admin\model\Order::where('pay_trade_no', $out_trade_no)->find();
        if (!$order) {
            $this->error("fail");
        }
        // $account = \app\admin\model\Account::where('account_identity', $order->account_identity)->find();
        // if (!$account) {
        //     $this->error("fail");
        // }
        // $account_config = json_decode($account->config,true);
        // $alipay_config = [
        //     'app_id' => $account_config['appid'],
        //     'cert_mode' => 0,
        //     'alipay_public_key' => $account_config['public_key'],
        //     'app_private_key' => $account_config['private_key'],
        //     'sign_type' => "RSA2",
        //     'charset' => "UTF-8",
        // ];
        // $aop = new \Alipay\AlipayTradeService($alipay_config);

        if($trade_status=="TRADE_SUCCESS"){
            if ($order->pay_status == 0) {
                // 修改支付状态
                $order->pay_status = 1;
                $order->save();
                $merchants = Merchants::where('id', $order->merchants_id)->find();

                // 给商户回调
                if (\app\common\library\Order::merchantsCallback($order->callback_url, $merchants->key, $order->out_trade_no, $order->amount, $order->channel_code, $order->pay_status)) {
                    $order->callback_status = 1;
                    $order->callback_time = time();
                    $order->save();
                    return 'success';
                } else {
                    return 'fail';
                }
            }

            if ($order->pay_status == 1 && $order->callback_status == 0) {
                $merchants = Merchants::where('id', $order->merchants_id)->find();
                // 给商户回调
                if (\app\common\library\Order::merchantsCallback($order->callback_url, $merchants->key, $order->out_trade_no, $order->amount, $order->channel_code, $order->pay_status)) {
                    $order->callback_status = 1;
                    $order->callback_time = time();
                    $order->save();
                    return 'success';
                } else {
                    return 'fail';
                }
            }

            return 'success';
        }
        return 'fail';
    }

    // 商户查询订单支付状态
    public function check()
    {
        if ($this->request->isPost()) {
            $data = $this->request->param();

            $rule = [
                'out_trade_no'  => 'require',
                'merchants_code'  => 'require',
                'channel_code' => 'require',
                'sign' => 'require'
            ];

            $msg = [
                'out_trade_no.require' => '订单号必须',
                'merchants_code.require' => '商户编码必须',
                'channel_code.require' => '通道编码必须',
                'sign.require' => '签名必须'
            ];

            $validate = Validate::make($rule,$msg);
            $result = $validate->check($data);

            if(!$result) {
                $this->error($validate->getError());
            }

            $merchants = Merchants::where('code', $data['merchants_code'])->find();
            if (!$merchants) {
                $this->error('商户不存在');
            }

            $sign = md5(\app\common\library\Order::ascii($data) . "&key=" . $merchants->key);
            if ($sign != $data['sign']) {
                $this->error(__('签名错误'),$sign);
            }

            $order = \app\admin\model\Order::where('out_trade_no',$data['out_trade_no'])->find();
            if (!$order) {
                $this->error('订单不存在');
            }

            if ($order['pay_status']==1) {
                $this->success('订单已支付');
            }

        }
    }

    public function get_by_outno()
    {
        $pay_trade_no = input('pay_trade_no');
        // $create_ip = input('ip');
        if (empty($pay_trade_no)) $this->error("pay_trade_no 不能为空");
        $order = \app\admin\model\Order::where('pay_trade_no', $pay_trade_no)->find();
        if (!$order) {
            $this->error("订单不存在");
        }
        $order->notes = "已到达支付页";
        // $order->create_ip = $create_ip;
        $order->save();
        if (abs(time() - $order->create_time)>300) $this->error("订单已过期");
        $account = \app\admin\model\Account::where('account_identity',$order->account_identity)->find();
        $data['appid'] = json_decode($account['config'],true)['appid'];
        $this->success("请求成功",$data);
    }
    
    public function get_by_web()
    {
        $host = input('host');
        // $create_ip = input('ip');
        if (empty($host)) $this->error("host 不能为空");

        $account = db('account')->where('config', "like", "%{$host}%")->find();

        if (!$account) {
            $this->error("支付宝不存在");
        }
        $data['appid'] = json_decode($account['config'],true)['appid'];
        $this->success("请求成功",$data);
    }

    protected function getAdminCanUse()
    {
        $adminList = db('admin')
            ->where('id', '>', 2)
            ->where('status', 'normal')
            ->order("pulltime ASC")
            ->select();

        foreach ($adminList as $key => $admin) {
            $account = db('account')->where('admin_id',$admin['id'])->where('status','normal')->find();
            if (empty($account)) {
                unset($adminList[$key]);
            }
        }
        $adminList = array_values($adminList);
        db('admin')->where('id',$adminList[0]['id'])->update(['pulltime'=>date("Y-m-d H:i:s", time())]);
        return $adminList[0];
    }

    protected function getAccountCanUseList2($amount, $channel_code)
    {
        $channel = Channel::where('code', $channel_code)->find();
        $accountList = db('account')
            ->whereNotLike('statusinfo','%异常%')
            ->where('channel', $channel_code)
            ->where('status', 'normal')
            ->where('min_price', '<=', $amount)
            ->where('max_price', '>=', $amount)
            ->order('pulltime ASE')
            ->select();

        foreach ($accountList as $key => $value) {
            // 检查账号所属的创建者是否开启
            $admin = db('admin')->where('id', $value['admin_id'])->find();
            if (!$admin || $admin['status'] != 'normal') {
                unset($accountList[$key]);
                continue;
            }

            // 是否点单码 9008
            if ($channel_code == '9008' || $channel_code == '9011' || $channel_code == '9012' ) {
                // 检查该账号是否有对应金额的商品
                $productExists = db('account_product')
                    ->where('account_id', $value['id'])
                    ->where('price', $amount)
                    ->find();
                
                // 如果没有对应金额的商品，则过滤此账号
                if (!$productExists) {
                    unset($accountList[$key]);
                    continue;
                }
            }

            // 通道拉单间隔
            if ($channel->pull_interval>0) {
                $fiveMinAgoOrderCount = db('order')
                    ->where('account_identity',$value['account_identity'])
                    ->where('amount', '=', $amount)
                    ->where('pay_status', 0)
                    ->whereTime('create_time', '-'.$channel->pull_interval.' seconds')
                    ->lock(true)
                    ->find();

                if (!empty($fiveMinAgoOrderCount)) {
                    unset($accountList[$key]);
                }
            }
            
            // 每日收款金额限制
            if($value['maximum'] > 0){
                // 今日已收款金额
                $todayPaidAmount = db('order')
                    ->where('account_identity',$value['account_identity'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'today')
                    ->sum('amount');

                // 如果今日已收款金额+当前订单金额 > 每日收款限额，则过滤此账号
                if (($todayPaidAmount + $amount) > $value['maximum']){
                    unset($accountList[$key]);
                }
            }

            // 今日收款笔数限制
            if($value['pay_limit']>0){
                // 今日已支付订单数量
                $todayIspayCount = db('order')
                    ->where('account_identity',$value['account_identity'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'today')
                    ->count('id');

                // 今日已达到收款笔数限制
                if ($todayIspayCount >= $value['pay_limit']){
                    // db('account')->where('id',$value['id'])->update([
                    //     'statusinfo'=>'今日已达到收款笔数限制,请明日再试',
                    //     'status'=>'hidden'
                    // ]);
                    unset($accountList[$key]);
                }
            }

            // 检查连续未支付订单数量
            if($value['nopay_limit'] > 0){
                // 构建查询条件，获取该账号的订单，按创建时间倒序
                $orderQuery = db('order')
                    ->where('account_identity', $value['account_identity'])
                    ->order('create_time DESC');

                // 如果abnormal_time不为空，则从abnormal_time开始计算
                if (!empty($value['abnormal_time'])) {
                    $orderQuery->where('create_time', '>=', strtotime($value['abnormal_time']));
                }

                // 获取该账号的订单列表
                $orderList = $orderQuery->column('pay_status');

                // 计算连续未支付订单笔数
                $consecutiveUnpaidCount = 0;
                foreach ($orderList as $payStatus) {
                    if ($payStatus != 1) { // 未支付订单
                        $consecutiveUnpaidCount++;
                    } else { // 遇到已支付订单，停止计算
                        break;
                    }
                }

                // 当连续未支付订单笔数达到或超过限制时，暂停账号并更新abnormal_time
                if ($consecutiveUnpaidCount >= $value['nopay_limit']) {
                    db('account')->where('id', $value['id'])->update([
                        'statusinfo' => '连续未支付>=' . $value['nopay_limit'] . '笔，请检查账号',
                        'status' => 'hidden',
                        'abnormal_time' => date('Y-m-d H:i:s')  // 更新abnormal_time为当前时间
                    ]);
                    unset($accountList[$key]);
                }
            }
            
        }
        $accountList = array_values($accountList);
        return $accountList;
    }

    public function statusinfo()
    {
        $msg = input("msg");
        $mid = input("mid");
        db('account')->where('mid',$mid)->update(['statusinfo'=>$msg]);
        return "ok";
    }

    /**
     * 将图片转换为base64格式
     * @param string $imageUrl 图片地址
     * @return string base64编码的图片数据
     */
    private function convertImageToBase64($imageUrl)
    {
        try {
            // 如果是相对路径，转换为完整URL
            if (strpos($imageUrl, 'http') !== 0) {
                $imageUrl = request()->domain() . $imageUrl;
            }

            Log::info('开始转换二维码为base64: ' . $imageUrl);

            // 获取图片内容
            $imageData = $this->getImageData($imageUrl);
            if (empty($imageData)) {
                Log::error('获取图片数据失败: ' . $imageUrl);
                return '';
            }

            // 检测图片类型
            $imageInfo = $this->getImageInfo($imageData);
            if (!$imageInfo) {
                Log::error('无法识别图片格式: ' . $imageUrl);
                return '';
            }

            // 转换为base64
            $base64 = base64_encode($imageData);
            $base64WithPrefix = 'data:' . $imageInfo['mime'] . ';base64,' . $base64;

            Log::info('二维码转换base64成功，大小: ' . strlen($base64) . ' 字符');
            return $base64WithPrefix;

        } catch (\Exception $e) {
            Log::error('二维码转换base64异常: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * 获取图片数据
     * @param string $imageUrl 图片URL
     * @return string|false 图片二进制数据
     */
    private function getImageData($imageUrl)
    {
        try {
            // 如果是本地文件
            if (strpos($imageUrl, request()->domain()) === 0) {
                $localPath = $this->getLocalImagePath($imageUrl);
                if (file_exists($localPath)) {
                    return file_get_contents($localPath);
                }
            }

            // 远程文件，使用HTTP请求获取
            $imageData = Http::get($imageUrl);
            if (!empty($imageData)) {
                return $imageData;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('获取图片数据失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取图片信息
     * @param string $imageData 图片二进制数据
     * @return array|false 图片信息
     */
    private function getImageInfo($imageData)
    {
        try {
            // 创建临时文件来检测图片类型
            $tempFile = tempnam(sys_get_temp_dir(), 'qrcode_');
            file_put_contents($tempFile, $imageData);

            $imageInfo = getimagesize($tempFile);
            unlink($tempFile); // 删除临时文件

            if ($imageInfo !== false) {
                return [
                    'width' => $imageInfo[0],
                    'height' => $imageInfo[1],
                    'type' => $imageInfo[2],
                    'mime' => $imageInfo['mime']
                ];
            }

            return false;
        } catch (\Exception $e) {
            Log::error('获取图片信息失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取图片的本地路径
     * @param string $imageUrl 图片URL或相对路径
     * @return string 本地文件路径
     */
    private function getLocalImagePath($imageUrl)
    {
        // 如果是相对路径，转换为绝对路径
        if (strpos($imageUrl, 'http') !== 0) {
            // 移除开头的斜杠
            $imageUrl = ltrim($imageUrl, '/');
            return ROOT_PATH . 'public' . DS . $imageUrl;
        }

        // 如果是完整URL，检查是否是本站的图片
        $domain = request()->domain();
        if (strpos($imageUrl, $domain) === 0) {
            $relativePath = str_replace($domain, '', $imageUrl);
            $relativePath = ltrim($relativePath, '/');
            return ROOT_PATH . 'public' . DS . $relativePath;
        }

        // 如果是外部URL，返回原URL（由getImageData方法处理）
        return $imageUrl;
    }

    public function setopenid()
    {
        $out_trade_no = input('out_trade_no');
        $openid = input('openid');
        if (empty($out_trade_no)) $this->error("out_trade_no 不能为空");
        $order = \app\admin\model\Order::where('out_trade_no', $out_trade_no)->find();
        if (!$order) {
            $this->error("订单不存在");
        }
        $order->openid = $openid;
        $order->save();
        $this->success("请求成功");
    }

    // 获取订单预授权状态
    public function getOrderStatus()
    {
        $params = input();
        if (empty($params['out_trade_no'])) $this->error("out_trade_no 不能为空");
        $order = \app\admin\model\Order::where('out_trade_no', $params['out_trade_no'])->find();
        if (!$order) $this->error("订单不存在");
        $order->notes = "已到达支付页";
        $order->create_ip = $this->request->ip();
        $order->save();
        
        $this->success("请求成功",$order);
    }
}


